{"id": "johnson-backstab-writing-form", "fields": [{"id": "date", "label": "记录日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "约翰逊传背刺写法记录日期"}, {"id": "recordType", "label": "记录类型", "type": "select", "options": [{"id": "client-complaint", "label": "客户吐槽记录", "value": "客户吐槽记录"}, {"id": "regulatory-warning", "label": "监管沟通警告", "value": "监管沟通警告"}, {"id": "industry-hypocrisy", "label": "行业虚伪现象", "value": "行业虚伪现象"}, {"id": "corporate-doublespeak", "label": "企业双重标准", "value": "企业双重标准"}, {"id": "expert-contradiction", "label": "专家自相矛盾", "value": "专家自相矛盾"}], "description": "选择背刺写法的记录类型"}, {"id": "surfacePhenomenon", "label": "表面现象", "type": "textarea", "rows": 2, "description": "客户说什么/官方说什么/表面上是什么"}, {"id": "hidden<PERSON>ru<PERSON>", "label": "背后真相", "type": "textarea", "rows": 3, "description": "实际上是什么/真正的动机是什么"}, {"id": "industryJargonTranslation", "label": "行业黑话翻译", "type": "textarea", "rows": 2, "description": "将官方说法翻译成大白话"}, {"id": "myInnerOS", "label": "我的内心OS", "type": "textarea", "rows": 2, "description": "你的内心吐槽或警告"}, {"id": "contradictionEvidence", "label": "矛盾证据", "type": "textarea", "rows": 3, "description": "支撑背刺观点的具体证据"}, {"id": "officialStatement", "label": "官方表态", "type": "text", "description": "监管/企业/专家的官方说法"}, {"id": "actualExecution", "label": "实际执行", "type": "textarea", "rows": 2, "description": "现实中实际是怎么做的"}, {"id": "enterpriseReaction", "label": "企业反应", "type": "textarea", "rows": 2, "description": "大家都在怎么应对"}, {"id": "riskWarning", "label": "风险预警", "type": "textarea", "rows": 2, "description": "⚠️ 真正的坑在哪里"}, {"id": "ironyLevel", "label": "讽刺程度", "type": "select", "options": [{"id": "mild", "label": "温和讽刺", "value": "温和讽刺"}, {"id": "moderate", "label": "适度讽刺", "value": "适度讽刺"}, {"id": "sharp", "label": "尖锐讽刺", "value": "尖锐讽刺"}, {"id": "brutal", "label": "残酷讽刺", "value": "残酷讽刺"}, {"id": "devastating", "label": "毁灭性讽刺", "value": "毁灭性讽刺"}], "description": "选择讽刺的程度"}, {"id": "targetAudience", "label": "目标读者", "type": "select", "options": [{"id": "industry-insiders", "label": "行业内部人士", "value": "行业内部人士"}, {"id": "enterprise-executives", "label": "企业高管", "value": "企业高管"}, {"id": "compliance-professionals", "label": "合规从业者", "value": "合规从业者"}, {"id": "general-public", "label": "普通大众", "value": "普通大众"}, {"id": "regulatory-officials", "label": "监管人员", "value": "监管人员"}, {"id": "media-journalists", "label": "媒体记者", "value": "媒体记者"}], "description": "这个背刺内容的目标读者"}, {"id": "writingStyle", "label": "写作风格", "type": "select", "options": [{"id": "academic-serious", "label": "学术严肃", "value": "学术严肃"}, {"id": "journalistic", "label": "新闻报道", "value": "新闻报道"}, {"id": "satirical", "label": "讽刺幽默", "value": "讽刺幽默"}, {"id": "conversational", "label": "对话式", "value": "对话式"}, {"id": "investigative", "label": "调查揭露", "value": "调查揭露"}, {"id": "opinion-piece", "label": "观点评论", "value": "观点评论"}], "description": "选择写作风格"}, {"id": "ethicalBoundary", "label": "伦理边界", "type": "select", "options": [{"id": "fact-based", "label": "基于事实", "value": "基于事实"}, {"id": "opinion-based", "label": "基于观点", "value": "基于观点"}, {"id": "speculation", "label": "合理推测", "value": "合理推测"}, {"id": "anonymous-source", "label": "匿名消息", "value": "匿名消息"}, {"id": "insider-info", "label": "内部信息", "value": "内部信息"}], "description": "内容的伦理边界"}, {"id": "publicationRisk", "label": "发布风险", "type": "select", "options": [{"id": "low-risk", "label": "低风险", "value": "低风险"}, {"id": "medium-risk", "label": "中等风险", "value": "中等风险"}, {"id": "high-risk", "label": "高风险", "value": "高风险"}, {"id": "legal-risk", "label": "法律风险", "value": "法律风险"}, {"id": "career-risk", "label": "职业风险", "value": "职业风险"}], "description": "评估发布这个内容的风险"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 约翰逊传背刺写法表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const recordType = form.recordType || '客户吐槽记录';\n    const surfacePhenomenon = form.surfacePhenomenon || '表面现象';\n    const hiddenTruth = form.hiddenTruth || '背后真相';\n    const industryJargonTranslation = form.industryJargonTranslation || '行业黑话翻译';\n    const myInnerOS = form.myInnerOS || '我的内心OS';\n    const contradictionEvidence = form.contradictionEvidence || '矛盾证据';\n    const officialStatement = form.officialStatement || '官方表态';\n    const actualExecution = form.actualExecution || '实际执行';\n    const enterpriseReaction = form.enterpriseReaction || '企业反应';\n    const riskWarning = form.riskWarning || '风险预警';\n    const ironyLevel = form.ironyLevel || '适度讽刺';\n    const targetAudience = form.targetAudience || '行业内部人士';\n    const writingStyle = form.writingStyle || '观点评论';\n    const ethicalBoundary = form.ethicalBoundary || '基于事实';\n    const publicationRisk = form.publicationRisk || '中等风险';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\nrecordType: ${recordType}\\nironyLevel: ${ironyLevel}\\ntargetAudience: ${targetAudience}\\nwritingStyle: ${writingStyle}\\nethicalBoundary: ${ethicalBoundary}\\npublicationRisk: ${publicationRisk}\\ntags:\\n  - 约翰逊传背刺写法\\n  - ${recordType}\\n  - ${ironyLevel}\\n  - ${targetAudience}\\n  - ${publicationRisk}\\ncreatedBy: 约翰逊传背刺写法系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# 约翰逊传背刺写法记录\\n\\n## 📋 基础信息\\n\\n**记录日期：** ${date}\\n**记录类型：** ${recordType}\\n**讽刺程度：** ${ironyLevel}\\n**目标读者：** ${targetAudience}\\n**写作风格：** ${writingStyle}\\n**发布风险：** ${publicationRisk}\\n\\n## 🎭 表象与真相\\n\\n### 表面现象\\n${surfacePhenomenon}\\n\\n### 背后真相\\n${hiddenTruth}\\n\\n### 行业黑话翻译\\n${industryJargonTranslation}\\n\\n### 我的内心OS\\n> ${myInnerOS}\\n\\n## 📊 监管沟通分析\\n\\n### 官方表态\\n**官方说：** \"${officialStatement}\"\\n\\n### 实际执行\\n**现实中：** ${actualExecution}\\n\\n### 企业反应\\n**大家都在：** ${enterpriseReaction}\\n\\n### ⚠️ 风险预警\\n**真正的坑在：** ${riskWarning}\\n\\n## 🔍 矛盾证据\\n\\n${contradictionEvidence}\\n\\n## ⚖️ 伦理考量\\n\\n**内容边界：** ${ethicalBoundary}\\n**发布风险评估：** ${publicationRisk}\\n\\n---\\n\\n**快速标签：** #${recordType} #${ironyLevel} #${publicationRisk}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位擅长约翰逊传背刺写法的资深作家。请基于以下素材，创作一篇${ironyLevel}程度的${writingStyle}风格文章，目标读者是${targetAudience}：\\n\\n素材内容：\\n${baseTemplate}\\n\\n请运用约翰逊传背刺写法的精髓：\\n1. 表面客观，暗藏锋芒\\n2. 用事实说话，让读者自己得出结论\\n3. 适度的讽刺和反讽\\n4. 揭露表象与真相的差距\\n5. 保持专业性，避免过度情绪化\\n\\n注意伦理边界：${ethicalBoundary}，控制发布风险在${publicationRisk}范围内。\\n\\n字数控制在800-1200字。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI背刺写法创作暂时不可用，请手动补充约翰逊传风格内容)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## ✍️ AI约翰逊传背刺创作\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 发布策略记录\\n\\n<!-- 记录这个内容的发布策略和效果 -->\\n\\n## 🔄 内容迭代优化\\n\\n<!-- 记录内容的进一步完善和风险控制 -->\\n\\n---\\n\\n*创作时间：${new Date().toLocaleString('zh-CN')} | AI背刺创作：DeepSeek | 约翰逊传写法库*`;\n    \n    const fileName = `约翰逊背刺-${recordType}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/约翰逊传背刺写法/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/约翰逊传背刺写法';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`约翰逊传背刺写法记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 约翰逊传背刺写法记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成约翰逊传背刺写法记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "约翰逊传背刺写法表单"}