{"id": "personal-observation-research-form", "fields": [{"id": "date", "label": "研究日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "个人观察研究记录日期"}, {"id": "researchTitle", "label": "研究主题", "type": "text", "description": "用一句话概括这个观察研究的主题"}, {"id": "personalObservation", "label": "个人观察🧐", "type": "textarea", "rows": 3, "description": "我发现了什么（你的独特观察）"}, {"id": "existingResearch", "label": "过往研究", "type": "textarea", "rows": 3, "description": "已有研究说了什么（学术界/行业的主流观点）"}, {"id": "researchCase", "label": "调研案例", "type": "textarea", "rows": 3, "description": "具体案例是什么（支撑观察的实际案例）"}, {"id": "conflictPoint", "label": "冲突点", "type": "textarea", "rows": 3, "description": "观察与研究的矛盾在哪里"}, {"id": "newInsight", "label": "新洞察", "type": "textarea", "rows": 3, "description": "我的独特发现（与主流观点的差异）"}, {"id": "observation<PERSON><PERSON><PERSON>", "label": "观察方法", "type": "select", "options": [{"id": "participant-observation", "label": "参与式观察", "value": "参与式观察"}, {"id": "non-participant-observation", "label": "非参与式观察", "value": "非参与式观察"}, {"id": "structured-observation", "label": "结构化观察", "value": "结构化观察"}, {"id": "unstructured-observation", "label": "非结构化观察", "value": "非结构化观察"}, {"id": "longitudinal-study", "label": "纵向研究", "value": "纵向研究"}, {"id": "cross-sectional-study", "label": "横向研究", "value": "横向研究"}, {"id": "case-study", "label": "案例研究", "value": "案例研究"}], "description": "选择主要的观察研究方法"}, {"id": "dataSource", "label": "数据来源", "type": "select", "options": [{"id": "direct-experience", "label": "直接经验", "value": "直接经验"}, {"id": "client-feedback", "label": "客户反馈", "value": "客户反馈"}, {"id": "industry-survey", "label": "行业调研", "value": "行业调研"}, {"id": "academic-literature", "label": "学术文献", "value": "学术文献"}, {"id": "regulatory-data", "label": "监管数据", "value": "监管数据"}, {"id": "market-report", "label": "市场报告", "value": "市场报告"}, {"id": "peer-discussion", "label": "同行讨论", "value": "同行讨论"}], "description": "主要的数据来源"}, {"id": "sampleSize", "label": "样本规模", "type": "select", "options": [{"id": "single-case", "label": "单一案例", "value": "单一案例"}, {"id": "small-sample", "label": "小样本（2-10）", "value": "小样本"}, {"id": "medium-sample", "label": "中等样本（11-50）", "value": "中等样本"}, {"id": "large-sample", "label": "大样本（50+）", "value": "大样本"}, {"id": "population-study", "label": "总体研究", "value": "总体研究"}], "description": "观察研究的样本规模"}, {"id": "timeSpan", "label": "时间跨度", "type": "select", "options": [{"id": "snapshot", "label": "瞬时观察", "value": "瞬时观察"}, {"id": "short-term", "label": "短期（1周内）", "value": "短期"}, {"id": "medium-term", "label": "中期（1个月内）", "value": "中期"}, {"id": "long-term", "label": "长期（3个月以上）", "value": "长期"}, {"id": "ongoing", "label": "持续观察", "value": "持续观察"}], "description": "观察的时间跨度"}, {"id": "reliabilityLevel", "label": "可靠性水平", "type": "select", "options": [{"id": "anecdotal", "label": "轶事证据", "value": "轶事证据"}, {"id": "preliminary", "label": "初步发现", "value": "初步发现"}, {"id": "moderate", "label": "中等可靠", "value": "中等可靠"}, {"id": "high", "label": "高可靠性", "value": "高可靠性"}, {"id": "validated", "label": "已验证", "value": "已验证"}], "description": "评估观察结果的可靠性"}, {"id": "biasConsideration", "label": "偏见考虑", "type": "textarea", "rows": 2, "description": "可能存在的观察偏见或局限性"}, {"id": "verificationMethod", "label": "验证方法", "type": "textarea", "rows": 2, "description": "如何验证这个观察的准确性"}, {"id": "practicalImplication", "label": "实践意义", "type": "textarea", "rows": 3, "description": "这个发现对实际工作的指导意义"}, {"id": "future<PERSON>ese<PERSON><PERSON>", "label": "后续研究方向", "type": "textarea", "rows": 2, "description": "基于这个观察可以深入研究的方向"}, {"id": "publicationPotential", "label": "发表潜力", "type": "select", "options": [{"id": "internal-use", "label": "内部使用", "value": "内部使用"}, {"id": "industry-sharing", "label": "行业分享", "value": "行业分享"}, {"id": "conference-presentation", "label": "会议演讲", "value": "会议演讲"}, {"id": "academic-paper", "label": "学术论文", "value": "学术论文"}, {"id": "book-chapter", "label": "书籍章节", "value": "书籍章节"}, {"id": "media-article", "label": "媒体文章", "value": "媒体文章"}], "description": "这个研究的发表潜力"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 个人观察研究表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const researchTitle = form.researchTitle || '个人观察研究';\n    const personalObservation = form.personalObservation || '个人观察';\n    const existingResearch = form.existingResearch || '过往研究';\n    const researchCase = form.researchCase || '调研案例';\n    const conflictPoint = form.conflictPoint || '冲突点';\n    const newInsight = form.newInsight || '新洞察';\n    const observationMethod = form.observationMethod || '参与式观察';\n    const dataSource = form.dataSource || '直接经验';\n    const sampleSize = form.sampleSize || '小样本';\n    const timeSpan = form.timeSpan || '中期';\n    const reliabilityLevel = form.reliabilityLevel || '中等可靠';\n    const biasConsideration = form.biasConsideration || '偏见考虑';\n    const verificationMethod = form.verificationMethod || '验证方法';\n    const practicalImplication = form.practicalImplication || '实践意义';\n    const futureResearch = form.futureResearch || '后续研究方向';\n    const publicationPotential = form.publicationPotential || '行业分享';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\ntitle: ${researchTitle}\\nobservationMethod: ${observationMethod}\\ndataSource: ${dataSource}\\nsampleSize: ${sampleSize}\\ntimeSpan: ${timeSpan}\\nreliabilityLevel: ${reliabilityLevel}\\npublicationPotential: ${publicationPotential}\\ntags:\\n  - 个人观察研究\\n  - ${observationMethod}\\n  - ${dataSource}\\n  - ${reliabilityLevel}\\n  - ${publicationPotential}\\ncreatedBy: 个人观察研究系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# ${researchTitle}\\n\\n## 📋 研究基础信息\\n\\n**研究日期：** ${date}\\n**观察方法：** ${observationMethod}\\n**数据来源：** ${dataSource}\\n**样本规模：** ${sampleSize}\\n**时间跨度：** ${timeSpan}\\n**可靠性水平：** ${reliabilityLevel}\\n\\n## 🔍 三要素快速记录\\n\\n### 个人观察🧐\\n**我发现：** ${personalObservation}\\n\\n### 过往研究\\n**已有研究说：** ${existingResearch}\\n\\n### 调研案例\\n**具体案例是：** ${researchCase}\\n\\n## ⚡ 冲突与洞察\\n\\n### 冲突点\\n**观察与研究的矛盾：** ${conflictPoint}\\n\\n### 新洞察\\n**我的独特发现：** ${newInsight}\\n\\n## 🔬 研究质量评估\\n\\n### 偏见考虑\\n${biasConsideration}\\n\\n### 验证方法\\n${verificationMethod}\\n\\n## 💡 价值与应用\\n\\n### 实践意义\\n${practicalImplication}\\n\\n### 后续研究方向\\n${futureResearch}\\n\\n### 发表潜力\\n**适合发表形式：** ${publicationPotential}\\n\\n---\\n\\n**快速标签：** #${observationMethod} #${reliabilityLevel} #${publicationPotential}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的研究方法专家和学者。请基于以下个人观察研究记录，提供深度的学术分析和研究建议：\\n\\n研究记录：\\n${baseTemplate}\\n\\n请从以下角度进行分析：\\n1. 观察方法的科学性评估\\n2. 与现有理论的关系分析\\n3. 研究发现的创新性评价\\n4. 潜在的研究局限性\\n5. 进一步验证的研究设计建议\\n6. 学术价值和实践价值评估\\n7. 可能的发表策略和目标期刊\\n\\n要求语言严谨学术，体现研究方法的专业性和前瞻性。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.7,\n          max_tokens: 2500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI学术分析暂时不可用，请手动补充研究方法分析)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🎓 AI学术研究分析\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 研究进展记录\\n\\n<!-- 记录这个观察研究的后续进展和验证 -->\\n\\n## 🔄 研究迭代完善\\n\\n<!-- 记录研究方法和发现的进一步完善 -->\\n\\n---\\n\\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI学术分析：DeepSeek | 个人观察研究库*`;\n    \n    const fileName = `个人观察研究-${observationMethod}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/个人观察研究/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/个人观察研究';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`个人观察研究记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 个人观察研究记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成个人观察研究记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "个人观察研究表单"}