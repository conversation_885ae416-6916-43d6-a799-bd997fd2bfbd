#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
免责条款汇总生成器
从提取的JSON数据中生成分类整理的免责条款清单
"""

import json
import re
from pathlib import Path
from datetime import datetime
from collections import defaultdict

class DisclaimerSummarizer:
    def __init__(self, json_file_path: str):
        self.json_file = Path(json_file_path)
        self.data = self.load_data()
        
    def load_data(self):
        """加载JSON数据"""
        with open(self.json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def categorize_clauses(self):
        """对免责条款进行分类"""
        categories = {
            '服务范围限制': [],
            '责任免除': [],
            '风险提示': [],
            '合同条款': [],
            '自动续约': [],
            '费用相关': [],
            '数据安全': [],
            '法律合规': [],
            '其他免责': []
        }
        
        # 分类关键词
        category_keywords = {
            '服务范围限制': ['服务不包含', '不提供', '仅供参考', '建议', '咨询'],
            '责任免除': ['不承担责任', '免责', '责任限制', '概不负责', '自行承担'],
            '风险提示': ['风险', '注意', '警告', '提醒', '可能'],
            '合同条款': ['第.*条', '条款', '协议', '合同', '约定'],
            '自动续约': ['自动续约', '续期', '续费', '到期'],
            '费用相关': ['费用', '价格', '收费', '付款', '成本'],
            '数据安全': ['数据', '隐私', '信息', '泄露', '安全'],
            '法律合规': ['法律', '合规', '监管', '处罚', '违法']
        }
        
        # 遍历所有文件的条款
        for file_data in self.data['files']:
            # 处理免责条款
            for clause in file_data['disclaimer_clauses']:
                categorized = False
                for category, keywords in category_keywords.items():
                    if any(re.search(keyword, clause, re.IGNORECASE) for keyword in keywords):
                        categories[category].append({
                            'text': clause,
                            'source': file_data['file_name']
                        })
                        categorized = True
                        break
                
                if not categorized:
                    categories['其他免责'].append({
                        'text': clause,
                        'source': file_data['file_name']
                    })
            
            # 处理合同条款
            for clause in file_data['contract_clauses']:
                categories['合同条款'].append({
                    'text': clause,
                    'source': file_data['file_name']
                })
            
            # 处理风险提示
            for warning in file_data['risk_warnings']:
                categories['风险提示'].append({
                    'text': warning,
                    'source': file_data['file_name']
                })
        
        return categories
    
    def extract_key_templates(self, categories):
        """提取关键模板条款"""
        templates = {
            '标准免责模板': [],
            '服务限制模板': [],
            '责任划分模板': [],
            '风险告知模板': []
        }
        
        # 查找包含模板特征的条款
        for category, clauses in categories.items():
            for clause_data in clauses:
                clause = clause_data['text']
                
                # 标准免责模板
                if any(keyword in clause for keyword in ['乙方不承担', '甲方理解', '仅供参考']):
                    templates['标准免责模板'].append(clause_data)
                
                # 服务限制模板
                elif any(keyword in clause for keyword in ['服务不包含', '不提供', '另行约定']):
                    templates['服务限制模板'].append(clause_data)
                
                # 责任划分模板
                elif any(keyword in clause for keyword in ['责任限制', '责任上限', '自行承担']):
                    templates['责任划分模板'].append(clause_data)
                
                # 风险告知模板
                elif any(keyword in clause for keyword in ['风险提示', '注意事项', '可能导致']):
                    templates['风险告知模板'].append(clause_data)
        
        return templates
    
    def generate_summary_report(self, output_file: str):
        """生成汇总报告"""
        categories = self.categorize_clauses()
        templates = self.extract_key_templates(categories)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 免责条款分类汇总报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**数据来源**: {self.json_file.name}\n")
            f.write(f"**处理文件数**: {self.data['total_files_processed']}\n\n")
            
            # 统计信息
            total_clauses = sum(len(clauses) for clauses in categories.values())
            f.write(f"**总条款数**: {total_clauses}\n\n")
            
            f.write("## 📊 分类统计\n\n")
            for category, clauses in categories.items():
                if clauses:
                    f.write(f"- **{category}**: {len(clauses)} 条\n")
            f.write("\n")
            
            # 关键模板
            f.write("## 🎯 关键免责模板\n\n")
            for template_type, template_clauses in templates.items():
                if template_clauses:
                    f.write(f"### {template_type}\n\n")
                    for i, clause_data in enumerate(template_clauses[:3], 1):  # 只显示前3个
                        f.write(f"**模板 {i}**:\n")
                        f.write(f"```\n{clause_data['text'][:200]}...\n```\n")
                        f.write(f"*来源: {clause_data['source']}*\n\n")
            
            # 分类详细内容
            f.write("## 📋 分类详细内容\n\n")
            for category, clauses in categories.items():
                if not clauses:
                    continue
                    
                f.write(f"### {category} ({len(clauses)} 条)\n\n")
                
                for i, clause_data in enumerate(clauses, 1):
                    f.write(f"#### {i}. {clause_data['source']}\n\n")
                    # 限制显示长度
                    text = clause_data['text']
                    if len(text) > 300:
                        text = text[:300] + "..."
                    f.write(f"{text}\n\n")
                    f.write("---\n\n")
    
    def generate_template_library(self, output_file: str):
        """生成可复用的模板库"""
        categories = self.categorize_clauses()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 免责条款模板库\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 使用说明\n\n")
            f.write("本模板库包含从实际案例中提取的免责条款，可根据具体情况进行修改使用。\n\n")
            f.write("⚠️ **重要提醒**: 使用前请咨询专业法律人士，确保条款的合法性和有效性。\n\n")
            
            # 按类别生成模板
            template_categories = {
                '服务范围限制': '用于明确服务边界，避免客户期望过高',
                '责任免除': '用于合理限制服务提供方的责任范围',
                '风险提示': '用于提前告知客户可能面临的风险',
                '自动续约': '用于设置合同续约机制',
                '费用相关': '用于明确费用标准和支付条件'
            }
            
            for category, description in template_categories.items():
                if category in categories and categories[category]:
                    f.write(f"## {category}\n\n")
                    f.write(f"**用途**: {description}\n\n")
                    
                    # 选择最具代表性的条款作为模板
                    representative_clauses = categories[category][:5]  # 取前5个
                    
                    for i, clause_data in enumerate(representative_clauses, 1):
                        f.write(f"### 模板 {i}\n\n")
                        f.write("```\n")
                        f.write(clause_data['text'])
                        f.write("\n```\n\n")
                        f.write(f"*参考来源: {clause_data['source']}*\n\n")
                        f.write("---\n\n")

def main():
    """主函数"""
    print("=" * 60)
    print("免责条款汇总生成器")
    print("=" * 60)
    
    # 查找最新的JSON文件
    extracted_dir = Path("extracted_disclaimers")
    if not extracted_dir.exists():
        print("错误: 未找到 extracted_disclaimers 目录!")
        print("请先运行 extract_disclaimer_clauses.py")
        return
    
    json_files = list(extracted_dir.glob("disclaimer_extraction_*.json"))
    if not json_files:
        print("错误: 未找到提取的JSON文件!")
        return
    
    # 使用最新的JSON文件
    latest_json = max(json_files, key=lambda x: x.stat().st_mtime)
    print(f"使用数据文件: {latest_json.name}")
    
    # 创建汇总器
    summarizer = DisclaimerSummarizer(latest_json)
    
    # 生成汇总报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    summary_file = extracted_dir / f"disclaimer_summary_{timestamp}.md"
    template_file = extracted_dir / f"disclaimer_templates_{timestamp}.md"
    
    print("正在生成汇总报告...")
    summarizer.generate_summary_report(summary_file)
    
    print("正在生成模板库...")
    summarizer.generate_template_library(template_file)
    
    print(f"\n生成完成!")
    print(f"汇总报告: {summary_file}")
    print(f"模板库: {template_file}")
    print("=" * 60)

if __name__ == "__main__":
    main()
