#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
免责条款批量提取脚本
用于从指定目录中的Markdown文件中提取免责条款、合规条款和法律风险相关内容
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

class DisclaimerExtractor:
    def __init__(self, source_dir: str, output_dir: str = "extracted_disclaimers"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 免责条款关键词模式
        self.disclaimer_patterns = [
            r'免责.*?条款',
            r'责任.*?限制',
            r'不承担.*?责任',
            r'甲方.*?理解.*?同意',
            r'乙方.*?不.*?承担',
            r'风险.*?告知',
            r'自行承担.*?责任',
            r'概不.*?负责',
            r'仅供参考',
            r'不保证.*?结果',
            r'自动续约',
            r'服务.*?不包含',
            r'责任.*?上限',
            r'法律.*?风险',
            r'合规.*?要求',
            r'监管.*?风险'
        ]
        
        # 合同条款模式
        self.contract_patterns = [
            r'第.*?条.*?[：:]',
            r'条款.*?[：:]',
            r'协议.*?[：:]',
            r'合同.*?[：:]',
            r'约定.*?[：:]'
        ]
        
        # 风险提示模式
        self.risk_patterns = [
            r'风险.*?提示',
            r'注意.*?事项',
            r'警告.*?[：:]',
            r'重要.*?提醒',
            r'特别.*?说明'
        ]

    def extract_from_file(self, file_path: Path) -> Dict:
        """从单个文件中提取免责条款"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = {
                'file_name': file_path.name,
                'file_path': str(file_path),
                'disclaimer_clauses': [],
                'contract_clauses': [],
                'risk_warnings': [],
                'key_phrases': [],
                'extracted_time': datetime.now().isoformat()
            }
            
            # 提取免责条款
            result['disclaimer_clauses'] = self._extract_disclaimer_clauses(content)
            
            # 提取合同条款
            result['contract_clauses'] = self._extract_contract_clauses(content)
            
            # 提取风险提示
            result['risk_warnings'] = self._extract_risk_warnings(content)
            
            # 提取关键短语
            result['key_phrases'] = self._extract_key_phrases(content)
            
            return result
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return None

    def _extract_disclaimer_clauses(self, content: str) -> List[str]:
        """提取免责条款"""
        clauses = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 检查是否包含免责关键词
            for pattern in self.disclaimer_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # 提取上下文（当前行及前后各2行）
                    context_start = max(0, i-2)
                    context_end = min(len(lines), i+3)
                    context = '\n'.join(lines[context_start:context_end]).strip()
                    
                    if context not in clauses:
                        clauses.append(context)
                    break
        
        return clauses

    def _extract_contract_clauses(self, content: str) -> List[str]:
        """提取合同条款"""
        clauses = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是合同条款
            for pattern in self.contract_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # 提取完整条款（从当前行开始到下一个条款或空行）
                    clause_lines = [line]
                    j = i + 1
                    while j < len(lines):
                        next_line = lines[j].strip()
                        if not next_line or any(re.search(p, next_line, re.IGNORECASE) for p in self.contract_patterns):
                            break
                        clause_lines.append(next_line)
                        j += 1
                    
                    clause = '\n'.join(clause_lines).strip()
                    if clause not in clauses:
                        clauses.append(clause)
                    break
        
        return clauses

    def _extract_risk_warnings(self, content: str) -> List[str]:
        """提取风险提示"""
        warnings = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 检查是否包含风险提示关键词
            for pattern in self.risk_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # 提取上下文
                    context_start = max(0, i-1)
                    context_end = min(len(lines), i+4)
                    context = '\n'.join(lines[context_start:context_end]).strip()
                    
                    if context not in warnings:
                        warnings.append(context)
                    break
        
        return warnings

    def _extract_key_phrases(self, content: str) -> List[str]:
        """提取关键短语"""
        key_phrases = []
        
        # 定义关键短语模式
        phrase_patterns = [
            r'"[^"]*免责[^"]*"',
            r'"[^"]*不承担[^"]*"',
            r'"[^"]*责任[^"]*"',
            r'"[^"]*风险[^"]*"',
            r'「[^」]*免责[^」]*」',
            r'「[^」]*责任[^」]*」',
            r'> "[^"]*"',
            r'> [^\n]*责任[^\n]*',
            r'> [^\n]*免责[^\n]*'
        ]
        
        for pattern in phrase_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            key_phrases.extend(matches)
        
        # 去重并清理
        key_phrases = list(set([phrase.strip() for phrase in key_phrases if len(phrase.strip()) > 10]))
        
        return key_phrases

    def process_directory(self) -> Dict:
        """处理整个目录"""
        print(f"开始处理目录: {self.source_dir}")
        
        all_results = {
            'processing_time': datetime.now().isoformat(),
            'source_directory': str(self.source_dir),
            'total_files_processed': 0,
            'successful_extractions': 0,
            'files': []
        }
        
        # 遍历所有.md文件
        md_files = list(self.source_dir.glob('*.md'))
        print(f"找到 {len(md_files)} 个Markdown文件")
        
        for file_path in md_files:
            print(f"处理文件: {file_path.name}")
            all_results['total_files_processed'] += 1
            
            result = self.extract_from_file(file_path)
            if result:
                all_results['files'].append(result)
                all_results['successful_extractions'] += 1
                
                # 如果提取到内容，显示统计
                total_items = (len(result['disclaimer_clauses']) + 
                             len(result['contract_clauses']) + 
                             len(result['risk_warnings']) + 
                             len(result['key_phrases']))
                
                if total_items > 0:
                    print(f"  ✓ 提取到 {total_items} 项内容")
                else:
                    print(f"  - 未找到相关内容")
        
        return all_results

    def save_results(self, results: Dict):
        """保存提取结果"""
        # 保存完整结果为JSON
        json_file = self.output_dir / f"disclaimer_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 生成可读性强的汇总报告
        report_file = self.output_dir / f"disclaimer_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        self._generate_report(results, report_file)
        
        print(f"\n结果已保存:")
        print(f"  JSON文件: {json_file}")
        print(f"  报告文件: {report_file}")

    def _generate_report(self, results: Dict, report_file: Path):
        """生成可读性强的报告"""
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 免责条款提取报告\n\n")
            f.write(f"**处理时间**: {results['processing_time']}\n")
            f.write(f"**源目录**: {results['source_directory']}\n")
            f.write(f"**处理文件数**: {results['total_files_processed']}\n")
            f.write(f"**成功提取数**: {results['successful_extractions']}\n\n")
            
            f.write("## 提取结果汇总\n\n")
            
            for file_result in results['files']:
                if (len(file_result['disclaimer_clauses']) + 
                    len(file_result['contract_clauses']) + 
                    len(file_result['risk_warnings']) + 
                    len(file_result['key_phrases'])) == 0:
                    continue
                
                f.write(f"### 📄 {file_result['file_name']}\n\n")
                
                if file_result['disclaimer_clauses']:
                    f.write("#### 🚫 免责条款\n\n")
                    for i, clause in enumerate(file_result['disclaimer_clauses'], 1):
                        f.write(f"{i}. {clause}\n\n")
                
                if file_result['contract_clauses']:
                    f.write("#### 📋 合同条款\n\n")
                    for i, clause in enumerate(file_result['contract_clauses'], 1):
                        f.write(f"{i}. {clause}\n\n")
                
                if file_result['risk_warnings']:
                    f.write("#### ⚠️ 风险提示\n\n")
                    for i, warning in enumerate(file_result['risk_warnings'], 1):
                        f.write(f"{i}. {warning}\n\n")
                
                if file_result['key_phrases']:
                    f.write("#### 🔑 关键短语\n\n")
                    for i, phrase in enumerate(file_result['key_phrases'], 1):
                        f.write(f"{i}. {phrase}\n\n")
                
                f.write("---\n\n")

def main():
    """主函数"""
    # 设置源目录和输出目录
    source_directory = "免责"  # 相对于当前工作目录
    output_directory = "extracted_disclaimers"
    
    print("=" * 60)
    print("免责条款批量提取工具")
    print("=" * 60)
    
    # 创建提取器实例
    extractor = DisclaimerExtractor(source_directory, output_directory)
    
    # 检查源目录是否存在
    if not extractor.source_dir.exists():
        print(f"错误: 源目录 '{source_directory}' 不存在!")
        return
    
    # 处理目录
    results = extractor.process_directory()
    
    # 保存结果
    extractor.save_results(results)
    
    # 显示统计信息
    print("\n" + "=" * 60)
    print("提取完成!")
    print(f"总共处理了 {results['total_files_processed']} 个文件")
    print(f"成功提取了 {results['successful_extractions']} 个文件的内容")
    
    total_clauses = sum(
        len(f['disclaimer_clauses']) + len(f['contract_clauses']) + 
        len(f['risk_warnings']) + len(f['key_phrases'])
        for f in results['files']
    )
    print(f"总共提取了 {total_clauses} 项免责相关内容")
    print("=" * 60)

if __name__ == "__main__":
    main()
