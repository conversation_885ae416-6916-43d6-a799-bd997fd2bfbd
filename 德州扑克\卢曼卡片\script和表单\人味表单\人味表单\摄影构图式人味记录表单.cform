{"id": "photography-style-human-touch-form", "fields": [{"id": "date", "label": "记录日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "摄影构图式记录日期"}, {"id": "sceneTitle", "label": "场景标题", "type": "text", "description": "用一句话概括这个场景"}, {"id": "recordingTime", "label": "记录时机", "type": "select", "options": [{"id": "morning-observation", "label": "早晨观察", "value": "早晨观察"}, {"id": "after-client-meeting", "label": "客户会议后", "value": "客户会议后"}, {"id": "industry-event", "label": "行业活动中", "value": "行业活动中"}, {"id": "daily-work", "label": "日常工作", "value": "日常工作"}, {"id": "weekend-reflection", "label": "周末反思", "value": "周末反思"}, {"id": "random-moment", "label": "随机时刻", "value": "随机时刻"}], "description": "选择记录的时机"}, {"id": "wideShot", "label": "大景（行业生态）", "type": "textarea", "rows": 3, "description": "描述整个行业或大环境的状态"}, {"id": "ambientSound", "label": "环境音效", "type": "textarea", "rows": 2, "description": "记录环境中的声音、氛围、背景信息"}, {"id": "mediumShot", "label": "中景（工作场景）", "type": "textarea", "rows": 3, "description": "描述具体的工作场景或人际互动"}, {"id": "bodyLanguage", "label": "肢体语言", "type": "textarea", "rows": 2, "description": "观察到的肢体语言、姿态、动作"}, {"id": "closeUp", "label": "特写（关键瞬间）", "type": "textarea", "rows": 3, "description": "捕捉最关键的瞬间或细节"}, {"id": "microExpression", "label": "微表情/动作", "type": "textarea", "rows": 2, "description": "细微的表情变化或小动作"}, {"id": "emotionalTone", "label": "情感基调", "type": "select", "options": [{"id": "tense", "label": "紧张", "value": "紧张"}, {"id": "relaxed", "label": "轻松", "value": "轻松"}, {"id": "serious", "label": "严肃", "value": "严肃"}, {"id": "humorous", "label": "幽默", "value": "幽默"}, {"id": "melancholic", "label": "忧郁", "value": "忧郁"}, {"id": "exciting", "label": "兴奋", "value": "兴奋"}, {"id": "awkward", "label": "尴尬", "value": "尴尬"}], "description": "整个场景的情感基调"}, {"id": "keyDialogue", "label": "关键对话", "type": "textarea", "rows": 2, "description": "场景中的关键对话或声音"}, {"id": "visualDetails", "label": "视觉细节", "type": "textarea", "rows": 2, "description": "值得注意的视觉细节（颜色、物品、布置等）"}, {"id": "storyPotential", "label": "故事潜力", "type": "select", "options": [{"id": "opening-scene", "label": "开场场景", "value": "开场场景"}, {"id": "conflict-moment", "label": "冲突时刻", "value": "冲突时刻"}, {"id": "turning-point", "label": "转折点", "value": "转折点"}, {"id": "climax", "label": "高潮", "value": "高潮"}, {"id": "resolution", "label": "结局", "value": "结局"}, {"id": "background-info", "label": "背景信息", "value": "背景信息"}], "description": "这个场景在故事中的作用"}, {"id": "cinematicTechnique", "label": "电影技法", "type": "select", "options": [{"id": "parallel-montage", "label": "平行蒙太奇", "value": "平行蒙太奇"}, {"id": "contrast-montage", "label": "对比蒙太奇", "value": "对比蒙太奇"}, {"id": "metaphor-montage", "label": "隐喻蒙太奇", "value": "隐喻蒙太奇"}, {"id": "slow-motion", "label": "慢镜头", "value": "慢镜头"}, {"id": "quick-cut", "label": "快切", "value": "快切"}, {"id": "long-take", "label": "长镜头", "value": "长镜头"}, {"id": "flashback", "label": "闪回", "value": "闪回"}], "description": "适合的电影表现技法"}, {"id": "symbolism", "label": "象征意义", "type": "textarea", "rows": 2, "description": "这个场景的象征意义或隐喻"}, {"id": "applicationDirection", "label": "应用方向", "type": "select", "options": [{"id": "article-opening", "label": "文章开头", "value": "文章开头"}, {"id": "case-description", "label": "案例描述", "value": "案例描述"}, {"id": "story-material", "label": "故事素材", "value": "故事素材"}, {"id": "scene-setting", "label": "场景设定", "value": "场景设定"}, {"id": "atmosphere-creation", "label": "氛围营造", "value": "氛围营造"}, {"id": "character-development", "label": "人物塑造", "value": "人物塑造"}], "description": "这个场景记录的应用方向"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 摄影构图式人味记录表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const sceneTitle = form.sceneTitle || '场景记录';\n    const recordingTime = form.recordingTime || '客户会议后';\n    const wideShot = form.wideShot || '大景描述';\n    const ambientSound = form.ambientSound || '环境音效';\n    const mediumShot = form.mediumShot || '中景描述';\n    const bodyLanguage = form.bodyLanguage || '肢体语言';\n    const closeUp = form.closeUp || '特写描述';\n    const microExpression = form.microExpression || '微表情动作';\n    const emotionalTone = form.emotionalTone || '严肃';\n    const keyDialogue = form.keyDialogue || '关键对话';\n    const visualDetails = form.visualDetails || '视觉细节';\n    const storyPotential = form.storyPotential || '开场场景';\n    const cinematicTechnique = form.cinematicTechnique || '长镜头';\n    const symbolism = form.symbolism || '象征意义';\n    const applicationDirection = form.applicationDirection || '文章开头';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\ntitle: ${sceneTitle}\\nrecordingTime: ${recordingTime}\\nemotionalTone: ${emotionalTone}\\nstoryPotential: ${storyPotential}\\ncinematicTechnique: ${cinematicTechnique}\\napplicationDirection: ${applicationDirection}\\ntags:\\n  - 摄影构图式记录\\n  - ${recordingTime}\\n  - ${emotionalTone}\\n  - ${storyPotential}\\n  - ${cinematicTechnique}\\ncreatedBy: 摄影构图式人味记录系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# ${sceneTitle}\\n\\n## 📸 摄影构图式记录\\n\\n**记录日期：** ${date}\\n**记录时机：** ${recordingTime}\\n**情感基调：** ${emotionalTone}\\n**电影技法：** ${cinematicTechnique}\\n\\n## 🎬 景别记录法\\n\\n### 📺 大景（行业生态）\\n${wideShot}\\n\\n**环境音效：** ${ambientSound}\\n\\n### 🎭 中景（工作场景）\\n${mediumShot}\\n\\n**肢体语言：** ${bodyLanguage}\\n\\n### 🔍 特写（关键瞬间）\\n${closeUp}\\n\\n**微表情/动作：** ${microExpression}\\n\\n## 🎨 场景要素\\n\\n### 关键对话\\n> \"${keyDialogue}\"\\n\\n### 视觉细节\\n${visualDetails}\\n\\n### 象征意义\\n${symbolism}\\n\\n## 💡 创作价值\\n\\n**故事潜力：** ${storyPotential}\\n**应用方向：** ${applicationDirection}\\n\\n---\\n\\n**快速标签：** #${emotionalTone} #${storyPotential} #${cinematicTechnique}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位擅长电影叙事和场景构建的创作专家。请基于以下摄影构图式记录，进行深度的场景分析和创作建议：\\n\\n场景记录：\\n${baseTemplate}\\n\\n请从以下角度进行分析：\\n1. 场景的电影化表达技巧\\n2. 情感氛围的营造方法\\n3. 故事叙事的可能性\\n4. 人物塑造的细节挖掘\\n5. 象征意义的深度解读\\n6. 在不同媒介中的应用建议\\n\\n要求语言有画面感和电影感，体现专业的视觉叙事能力。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.9,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI场景分析暂时不可用，请手动补充电影化创作建议)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🎬 AI电影化场景分析\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 场景应用记录\\n\\n<!-- 记录这个场景在实际创作中的使用情况 -->\\n\\n## 🔄 场景迭代完善\\n\\n<!-- 记录场景的进一步加工和完善 -->\\n\\n---\\n\\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI场景分析：DeepSeek | 摄影构图式记录库*`;\n    \n    const fileName = `摄影构图-${storyPotential}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/摄影构图式记录/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/摄影构图式记录';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`摄影构图式记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 摄影构图式记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成摄影构图式记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "摄影构图式人味记录表单"}