{"id": "five-minute-daily-writing-form", "fields": [{"id": "date", "label": "记录日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "5分钟写作记录日期"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "观察到（具体行为/对话）", "type": "textarea", "rows": 2, "description": "第1分钟：捕捉1个「人味火花」"}, {"id": "myReaction", "label": "我的反应", "type": "select", "options": [{"id": "angry", "label": "愤怒", "value": "愤怒"}, {"id": "speechless", "label": "无语", "value": "无语"}, {"id": "enlightenment", "label": "顿悟", "value": "顿悟"}, {"id": "helpless", "label": "无奈", "value": "无奈"}, {"id": "shocked", "label": "震惊", "value": "震惊"}, {"id": "amused", "label": "好笑", "value": "好笑"}, {"id": "worried", "label": "担忧", "value": "担忧"}], "description": "记录情绪反应和争议点"}, {"id": "industryRule", "label": "行业潜规则（你看到的本质）", "type": "textarea", "rows": 2, "description": "透过现象看到的本质"}, {"id": "evidence", "label": "证据（截图/录音/邮件片段）", "type": "textarea", "rows": 2, "description": "支撑观察的具体证据"}, {"id": "aiInstruction", "label": "AI指令选择", "type": "select", "options": [{"id": "analysis", "label": "分析这个现象，给我3个深层原因", "value": "分析这个现象，给我3个深层原因"}, {"id": "case-study", "label": "用这个案例，写一个300字的风险警示", "value": "用这个案例，写一个300字的风险警示"}, {"id": "comparison", "label": "对比中美两国在这个问题上的不同做法", "value": "对比中美两国在这个问题上的不同做法"}, {"id": "advice", "label": "基于这个问题，给企业3个实操建议", "value": "基于这个问题，给企业3个实操建议"}, {"id": "critique", "label": "用犀利的语气吐槽这个行业现象", "value": "用犀利的语气吐槽这个行业现象"}, {"id": "custom", "label": "自定义指令", "value": "custom"}], "description": "第3分钟：选择对应AI指令"}, {"id": "customInstruction", "label": "自定义AI指令（如选择自定义）", "type": "text", "description": "当选择自定义指令时填写具体内容"}, {"id": "aiOutputNotes", "label": "AI输出修正", "type": "textarea", "rows": 3, "description": "第4分钟：标记有用部分，删除错误，补充内容"}, {"id": "archiveTags", "label": "归档标签", "type": "text", "description": "第5分钟：打标签分类（如：#删除权 #第5章）"}, {"id": "<PERSON><PERSON><PERSON>", "label": "个人化钩子", "type": "text", "description": "补充个人化钩子或后续思考点"}, {"id": "contentType", "label": "内容类型", "type": "select", "options": [{"id": "client-case", "label": "客户案例", "value": "客户案例"}, {"id": "regulation-update", "label": "法规更新", "value": "法规更新"}, {"id": "industry-observation", "label": "行业观察", "value": "行业观察"}, {"id": "personal-insight", "label": "个人洞察", "value": "个人洞察"}, {"id": "technical-discussion", "label": "技术讨论", "value": "技术讨论"}, {"id": "market-trend", "label": "市场趋势", "value": "市场趋势"}], "description": "选择内容的主要类型"}, {"id": "urgencyLevel", "label": "处理优先级", "type": "select", "options": [{"id": "hot-topic", "label": "热点话题（立即处理）", "value": "热点话题"}, {"id": "valuable", "label": "有价值（本周处理）", "value": "有价值"}, {"id": "archive", "label": "存档备用", "value": "存档备用"}], "description": "选择处理的优先级"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 5分钟每日写作行动表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const observedBehavior = form.observedBehavior || '观察到的行为';\n    const myReaction = form.myReaction || '无奈';\n    const industryRule = form.industryRule || '行业潜规则';\n    const evidence = form.evidence || '相关证据';\n    const aiInstruction = form.aiInstruction || '分析这个现象，给我3个深层原因';\n    const customInstruction = form.customInstruction || '';\n    const aiOutputNotes = form.aiOutputNotes || 'AI输出修正';\n    const archiveTags = form.archiveTags || '#行业观察';\n    const personalHook = form.personalHook || '个人化钩子';\n    const contentType = form.contentType || '行业观察';\n    const urgencyLevel = form.urgencyLevel || '有价值';\n    \n    const finalInstruction = aiInstruction === 'custom' ? (customInstruction || '自定义指令') : aiInstruction;\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\nmyReaction: ${myReaction}\\ncontentType: ${contentType}\\nurgencyLevel: ${urgencyLevel}\\narchiveTags: ${archiveTags}\\ntags:\\n  - 5分钟写作行动\\n  - ${contentType}\\n  - ${myReaction}\\n  - ${urgencyLevel}\\ncreatedBy: 5分钟每日写作行动系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# 5分钟每日写作行动记录\\n\\n## ⏱️ 5分钟流程记录\\n\\n**【日期】** ${date}\\n\\n### 第1分钟：人味火花捕捉\\n**观察到：** ${observedBehavior}\\n**我的反应：** ${myReaction}\\n\\n### 第2分钟：本质挖掘\\n**行业潜规则：** ${industryRule}\\n**证据：** ${evidence}\\n\\n### 第3分钟：AI指令选择\\n**选择的指令：** ${finalInstruction}\\n\\n### 第4分钟：AI输出修正\\n${aiOutputNotes}\\n\\n### 第5分钟：归档整理\\n**归档标签：** ${archiveTags}\\n**个人化钩子：** ${personalHook}\\n\\n## 📊 内容分类\\n\\n**内容类型：** ${contentType}\\n**处理优先级：** ${urgencyLevel}\\n\\n---\\n\\n**快速标签：** ${archiveTags}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `${finalInstruction}\\n\\n基于以下观察：\\n观察内容：${observedBehavior}\\n行业潜规则：${industryRule}\\n证据支撑：${evidence}\\n\\n请提供专业但带人味的分析，保持犀利的观察角度。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 1500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI扩写暂时不可用，请手动补充专业分析)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🤖 AI扩写内容\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 后续应用记录\\n\\n<!-- 记录这个素材在实际创作中的使用情况 -->\\n\\n## 🔄 内容迭代优化\\n\\n<!-- 记录内容的进一步加工和优化 -->\\n\\n---\\n\\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI扩写：DeepSeek | 5分钟写作库*`;\n    \n    const fileName = `5分钟写作-${contentType}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/5分钟写作行动/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/5分钟写作行动';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`5分钟写作行动记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 5分钟写作行动记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成5分钟写作行动记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "5分钟每日写作行动表单"}