# 免责条款批量提取工具使用指南

## 📋 工具概述

本工具包含两个主要脚本，用于从您的免责条款文档中批量提取和整理免责相关内容：

1. **`extract_disclaimer_clauses.py`** - 主提取脚本
2. **`generate_disclaimer_summary.py`** - 汇总分析脚本

## 🚀 快速开始

### 第一步：运行主提取脚本
```bash
python extract_disclaimer_clauses.py
```

这个脚本会：
- 扫描 `免责/` 目录下的所有 `.md` 文件
- 提取免责条款、合同条款、风险提示等内容
- 生成详细的JSON数据文件和可读报告

### 第二步：生成分类汇总
```bash
python generate_disclaimer_summary.py
```

这个脚本会：
- 对提取的内容进行智能分类
- 生成分类汇总报告
- 创建可复用的模板库

## 📊 提取结果说明

### 生成的文件

运行完成后，会在 `extracted_disclaimers/` 目录下生成以下文件：

1. **`disclaimer_extraction_YYYYMMDD_HHMMSS.json`**
   - 完整的提取数据（JSON格式）
   - 包含所有原始提取内容
   - 适合程序化处理

2. **`disclaimer_report_YYYYMMDD_HHMMSS.md`**
   - 详细的提取报告（Markdown格式）
   - 按文件组织的完整内容
   - 适合人工阅读

3. **`disclaimer_summary_YYYYMMDD_HHMMSS.md`**
   - 分类汇总报告
   - 包含统计信息和关键模板
   - 便于快速了解整体情况

4. **`disclaimer_templates_YYYYMMDD_HHMMSS.md`**
   - 可复用的模板库
   - 按用途分类的标准条款
   - 可直接用于合同起草

### 内容分类

工具会将提取的内容自动分为以下类别：

- **服务范围限制** - 明确服务边界的条款
- **责任免除** - 限制责任范围的条款  
- **风险提示** - 提前告知风险的条款
- **合同条款** - 标准合同条款
- **自动续约** - 续约相关条款
- **费用相关** - 费用和支付条款
- **数据安全** - 数据保护相关条款
- **法律合规** - 合规要求相关条款

## 🎯 实际应用建议

### 1. 合同起草
- 从模板库中选择合适的条款
- 根据具体业务场景进行修改
- 确保条款的合法性和有效性

### 2. 风险管控
- 参考风险提示类条款
- 建立完善的风险告知机制
- 做好客户沟通和确认

### 3. 业务标准化
- 使用标准化的免责条款
- 建立统一的服务协议模板
- 提高业务处理效率

## ⚠️ 重要提醒

1. **法律咨询**：使用任何条款前，请咨询专业法律人士
2. **合规性检查**：确保条款符合当地法律法规
3. **定期更新**：随着法律法规变化，及时更新条款
4. **客户沟通**：重要条款需要与客户充分沟通并确认

## 🔧 自定义配置

### 修改提取规则

如需调整提取规则，可以修改 `extract_disclaimer_clauses.py` 中的以下部分：

```python
# 免责条款关键词模式
self.disclaimer_patterns = [
    r'免责.*?条款',
    r'责任.*?限制',
    # 添加您的自定义模式
]
```

### 调整分类规则

可以修改 `generate_disclaimer_summary.py` 中的分类关键词：

```python
category_keywords = {
    '服务范围限制': ['服务不包含', '不提供', '仅供参考'],
    # 添加或修改分类规则
}
```

## 📈 统计信息

根据最近一次运行结果：

- **处理文件数**: 74个
- **成功提取数**: 74个  
- **总条款数**: 558项
- **主要分类**:
  - 服务范围限制: 114条
  - 合同条款: 127条
  - 风险提示: 73条
  - 责任免除: 19条

## 🆘 常见问题

### Q: 提取结果不准确怎么办？
A: 可以调整关键词模式，或者手动筛选结果

### Q: 如何处理特殊格式的文件？
A: 目前只支持Markdown格式，其他格式需要先转换

### Q: 可以处理其他语言的文档吗？
A: 需要修改关键词模式以适应其他语言

### Q: 如何批量处理多个目录？
A: 可以修改脚本中的 `source_directory` 参数

## 📞 技术支持

如果在使用过程中遇到问题，可以：

1. 检查文件路径和权限
2. 确认Python环境和依赖包
3. 查看错误日志信息
4. 根据需要调整配置参数

---

**最后更新**: 2025-08-03
**版本**: 1.0
